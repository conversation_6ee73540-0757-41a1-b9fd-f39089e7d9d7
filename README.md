# PDF to HTML Converter

Este projeto converte arquivos PDF para HTML mantendo a formatação exata, incluindo posicionamento de texto, linhas, formas e imagens.

## Características

- ✅ Preserva formatação exata do PDF
- ✅ Mantém posicionamento absoluto de elementos
- ✅ Extrai e preserva imagens
- ✅ Mantém linhas e formas geométricas
- ✅ Preserva fontes e tamanhos de texto
- ✅ Suporte a múltiplas páginas
- ✅ Output HTML responsivo

## Instalação

1. Instale as dependências:
```bash
pip install -r requirements.txt
```

## Uso

### Método 1: Script de exemplo (recomendado)
```bash
python convert_example.py
```

### Método 2: Linha de comando
```bash
python pdf_to_html_converter.py dock.pdf
```

### Método 3: Especificar diretório de saída
```bash
python pdf_to_html_converter.py dock.pdf -o custom_output_dir
```

### Método 4: Usar como módulo Python
```python
from pdf_to_html_converter import PDFToHTMLConverter

# Criar conversor
converter = PDFToHTMLConverter("dock.pdf", "terms/html")

# Converter
output_file = converter.convert()
print(f"HTML salvo em: {output_file}")
```

## Estrutura de Saída

O HTML gerado será salvo em `terms/html/` por padrão, com o mesmo nome do PDF original.

### Exemplo:
- Input: `dock.pdf`
- Output: `terms/html/dock.html`

## Como Funciona

O conversor utiliza a biblioteca `pdfplumber` para:

1. **Extrair texto com posicionamento**: Cada palavra é posicionada exatamente como no PDF original
2. **Extrair formas geométricas**: Linhas e retângulos são convertidos para elementos HTML/CSS
3. **Extrair imagens**: Imagens são convertidas para base64 e incorporadas no HTML
4. **Gerar CSS**: Estilos CSS são aplicados para manter o layout exato

## Dependências

- `pdfplumber`: Para extração de dados do PDF
- `Pillow`: Para processamento de imagens

## Limitações

- PDFs com texto em curvas complexas podem não ser perfeitamente reproduzidos
- Fontes personalizadas podem ser substituídas por fontes padrão
- PDFs muito grandes podem consumir bastante memória

## Troubleshooting

### Erro: "PDF file not found"
Certifique-se de que o arquivo PDF existe no diretório atual.

### Erro: "ModuleNotFoundError"
Execute: `pip install -r requirements.txt`

### Problemas de formatação
O conversor tenta manter a formatação exata, mas alguns PDFs complexos podem precisar de ajustes manuais no CSS gerado.

## Estrutura do Projeto

```
.
├── dock.pdf                    # Arquivo PDF de entrada
├── pdf_to_html_converter.py    # Conversor principal
├── convert_example.py          # Script de exemplo
├── requirements.txt            # Dependências
├── README.md                   # Este arquivo
└── terms/
    └── html/                   # Diretório de saída HTML
```
