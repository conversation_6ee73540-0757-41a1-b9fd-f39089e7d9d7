#!/usr/bin/env python3
"""
Advanced PDF to HTML Converter
Uses a hybrid approach combining text extraction with image rendering for perfect layout preservation.
"""

import pdfplumber
import argparse
import os
from pathlib import Path
import base64
from typing import Dict, List, Tuple, Optional
import json
import re
from io import BytesIO


class AdvancedPDFToHTMLConverter:
    def __init__(self, pdf_path: str, output_dir: str = "terms/html"):
        self.pdf_path = pdf_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # CSS styles for perfect layout preservation
        self.base_css = """
        <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
            padding: 20px;
        }
        .page-container {
            background-color: white;
            margin: 0 auto 30px auto;
            box-shadow: 0 4px 8px rgba(0,0,0,0.1);
            position: relative;
            border: 1px solid #ddd;
        }
        .page-content {
            position: relative;
            width: 100%;
            height: 100%;
        }
        .text-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
        }
        .text-element {
            position: absolute;
            font-family: inherit;
            white-space: nowrap;
            color: #000;
            user-select: text;
            cursor: text;
        }
        .background-layer {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 1;
        }
        .shape-element {
            position: absolute;
        }
        .line-element {
            background-color: #000;
        }
        .rect-element {
            border: 1px solid #000;
            background-color: transparent;
        }
        .highlight-element {
            background-color: yellow;
            opacity: 0.3;
        }
        @media print {
            body { background: white; padding: 0; }
            .page-container { 
                box-shadow: none; 
                border: none; 
                margin: 0; 
                page-break-after: always; 
            }
        }
        </style>
        """

    def extract_text_blocks(self, page) -> List[Dict]:
        """Extract text as coherent blocks with better spacing"""
        text_blocks = []
        
        # Use pdfplumber's built-in text extraction with layout
        try:
            # Extract words with their bounding boxes
            words = page.extract_words(
                x_tolerance=3,
                y_tolerance=3,
                keep_blank_chars=False,
                use_text_flow=True,
                horizontal_ltr=True,
                vertical_ttb=True,
                extra_attrs=['fontname', 'size']
            )
            
            for word in words:
                if word['text'].strip():
                    text_blocks.append({
                        'text': word['text'],
                        'x0': word['x0'],
                        'y0': word['top'],
                        'x1': word['x1'],
                        'y1': word['bottom'],
                        'font': word.get('fontname', 'Arial'),
                        'size': word.get('size', 12),
                        'width': word['x1'] - word['x0'],
                        'height': word['bottom'] - word['top']
                    })
        except Exception as e:
            print(f"Warning: Could not extract words properly: {e}")
            # Fallback to character-based extraction
            text_blocks = self._extract_chars_as_blocks(page)
        
        return text_blocks

    def _extract_chars_as_blocks(self, page) -> List[Dict]:
        """Fallback method to extract characters as individual blocks"""
        blocks = []
        chars = page.chars
        
        if not chars:
            return blocks
        
        # Group characters into words
        current_word = ""
        word_chars = []
        
        for i, char in enumerate(chars):
            text = char.get('text', '')
            
            if text.isspace() or i == len(chars) - 1:
                if current_word.strip() and word_chars:
                    first_char = word_chars[0]
                    last_char = word_chars[-1]
                    
                    blocks.append({
                        'text': current_word,
                        'x0': first_char.get('x0', 0),
                        'y0': first_char.get('top', 0),
                        'x1': last_char.get('x1', 0),
                        'y1': last_char.get('bottom', 0),
                        'font': first_char.get('fontname', 'Arial'),
                        'size': first_char.get('size', 12),
                        'width': last_char.get('x1', 0) - first_char.get('x0', 0),
                        'height': first_char.get('bottom', 0) - first_char.get('top', 0)
                    })
                
                current_word = ""
                word_chars = []
            else:
                current_word += text
                word_chars.append(char)
        
        return blocks

    def extract_shapes_and_lines(self, page) -> List[Dict]:
        """Extract lines and shapes with better precision"""
        elements = []
        
        # Extract lines
        try:
            lines = page.lines
            for line in lines:
                elements.append({
                    'type': 'line',
                    'x0': line.get('x0', 0),
                    'y0': line.get('top', 0),
                    'x1': line.get('x1', 0),
                    'y1': line.get('bottom', 0),
                    'width': line.get('linewidth', 1),
                    'stroke': line.get('stroke', '#000000')
                })
        except Exception as e:
            print(f"Warning: Could not extract lines: {e}")
        
        # Extract rectangles
        try:
            rects = page.rects
            for rect in rects:
                elements.append({
                    'type': 'rect',
                    'x0': rect.get('x0', 0),
                    'y0': rect.get('top', 0),
                    'x1': rect.get('x1', 0),
                    'y1': rect.get('bottom', 0),
                    'width': rect.get('linewidth', 1),
                    'stroke': rect.get('stroke', '#000000'),
                    'fill': rect.get('fill', 'transparent')
                })
        except Exception as e:
            print(f"Warning: Could not extract rectangles: {e}")
        
        return elements

    def extract_images_advanced(self, page) -> List[Dict]:
        """Extract images with better handling"""
        images = []
        
        try:
            page_images = page.images
            for img in page_images:
                try:
                    # Get the image area
                    bbox = (img['x0'], img['top'], img['x1'], img['bottom'])
                    cropped_page = page.crop(bbox)
                    
                    # Convert to image
                    pil_image = cropped_page.to_image(resolution=150)
                    
                    # Convert to base64
                    buffer = BytesIO()
                    pil_image.save(buffer, format='PNG')
                    img_data = base64.b64encode(buffer.getvalue()).decode()
                    
                    images.append({
                        'x': img['x0'],
                        'y': img['top'],
                        'width': img['width'],
                        'height': img['height'],
                        'data': f"data:image/png;base64,{img_data}"
                    })
                except Exception as e:
                    print(f"Warning: Could not process image: {e}")
        except Exception as e:
            print(f"Warning: Could not extract images: {e}")
        
        return images

    def normalize_font_family(self, font_name: str) -> str:
        """Convert PDF font names to web-safe CSS font families"""
        font_name = font_name.lower()
        
        # Common font mappings
        font_mappings = {
            'times': 'Times, "Times New Roman", serif',
            'timesnewroman': 'Times, "Times New Roman", serif',
            'helvetica': 'Arial, Helvetica, sans-serif',
            'arial': 'Arial, Helvetica, sans-serif',
            'courier': 'Courier, "Courier New", monospace',
            'symbol': 'Symbol, serif',
            'zapfdingbats': 'Zapf Dingbats, serif'
        }
        
        for key, value in font_mappings.items():
            if key in font_name:
                return value
        
        # Default fallback
        return 'Arial, Helvetica, sans-serif'

    def get_font_style_properties(self, font_name: str) -> Dict[str, str]:
        """Extract font weight and style from font name"""
        font_name = font_name.lower()
        
        properties = {
            'font-weight': 'normal',
            'font-style': 'normal'
        }
        
        if 'bold' in font_name:
            properties['font-weight'] = 'bold'
        if 'italic' in font_name:
            properties['font-style'] = 'italic'
        if 'oblique' in font_name:
            properties['font-style'] = 'oblique'
        
        return properties

    def convert_page_to_html(self, page, page_num: int) -> str:
        """Convert a single page to HTML with advanced layout preservation"""
        page_width = page.width
        page_height = page.height

        # Extract all elements
        text_blocks = self.extract_text_blocks(page)
        shapes = self.extract_shapes_and_lines(page)
        images = self.extract_images_advanced(page)

        html_content = f"""
        <div class="page-container" style="width: {page_width:.0f}px; height: {page_height:.0f}px;">
            <div class="page-content">
                <div class="background-layer">
        """

        # Add shapes and lines first (background layer)
        for shape in shapes:
            if shape['type'] == 'line':
                x0, y0 = shape['x0'], shape['y0']
                x1, y1 = shape['x1'], shape['y1']
                line_width = max(shape['width'], 0.5)

                # Calculate line properties
                import math
                length = math.sqrt((x1 - x0) ** 2 + (y1 - y0) ** 2)
                if length > 0:
                    angle = math.atan2(y1 - y0, x1 - x0) * 180 / math.pi

                    html_content += f"""
                    <div class="shape-element line-element" style="
                        left: {x0:.1f}px;
                        top: {y0:.1f}px;
                        width: {length:.1f}px;
                        height: {line_width:.1f}px;
                        transform: rotate({angle:.2f}deg);
                        transform-origin: 0 50%;
                        background-color: {shape.get('stroke', '#000')};
                    "></div>
                    """

            elif shape['type'] == 'rect':
                x0, y0 = shape['x0'], shape['y0']
                width = shape['x1'] - shape['x0']
                height = shape['y1'] - shape['y0']
                border_width = max(shape['width'], 0.5)
                stroke_color = shape.get('stroke', '#000')
                fill_color = shape.get('fill', 'transparent')

                html_content += f"""
                <div class="shape-element rect-element" style="
                    left: {x0:.1f}px;
                    top: {y0:.1f}px;
                    width: {width:.1f}px;
                    height: {height:.1f}px;
                    border: {border_width:.1f}px solid {stroke_color};
                    background-color: {fill_color};
                "></div>
                """

        # Add images
        for img in images:
            html_content += f"""
            <img class="shape-element" src="{img['data']}" style="
                left: {img['x']:.1f}px;
                top: {img['y']:.1f}px;
                width: {img['width']:.1f}px;
                height: {img['height']:.1f}px;
            ">
            """

        html_content += """
                </div>
                <div class="text-layer">
        """

        # Add text elements (foreground layer)
        for text_block in text_blocks:
            x = text_block['x0']
            y = text_block['y0']
            font_size = text_block['size']
            font_family = self.normalize_font_family(text_block['font'])
            font_props = self.get_font_style_properties(text_block['font'])
            text = self._escape_html_text(text_block['text'])

            html_content += f"""
            <div class="text-element" style="
                left: {x:.1f}px;
                top: {y:.1f}px;
                font-size: {font_size:.1f}px;
                font-family: {font_family};
                font-weight: {font_props['font-weight']};
                font-style: {font_props['font-style']};
                line-height: 1.0;
            ">{text}</div>
            """

        html_content += """
                </div>
            </div>
        </div>
        """

        return html_content

    def _escape_html_text(self, text: str) -> str:
        """Escape HTML special characters in text"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def convert(self) -> str:
        """Convert the entire PDF to HTML"""
        output_file = self.output_dir / f"{Path(self.pdf_path).stem}_advanced.html"

        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{Path(self.pdf_path).stem} - Advanced Conversion</title>
            {self.base_css}
        </head>
        <body>
        """

        with pdfplumber.open(self.pdf_path) as pdf:
            total_pages = len(pdf.pages)
            print(f"Converting {total_pages} pages with advanced algorithm...")

            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}/{total_pages}")
                page_html = self.convert_page_to_html(page, page_num)
                html_content += page_html

        html_content += """
        </body>
        </html>
        """

        # Save the HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)

        print(f"Advanced conversion completed! HTML saved to: {output_file}")
        return str(output_file)


def main():
    parser = argparse.ArgumentParser(description='Advanced PDF to HTML converter with perfect layout preservation')
    parser.add_argument('pdf_file', help='Path to the PDF file to convert')
    parser.add_argument('-o', '--output', default='terms/html',
                       help='Output directory (default: terms/html)')

    args = parser.parse_args()

    if not os.path.exists(args.pdf_file):
        print(f"Error: PDF file '{args.pdf_file}' not found!")
        return 1

    try:
        converter = AdvancedPDFToHTMLConverter(args.pdf_file, args.output)
        output_file = converter.convert()
        print(f"Successfully converted '{args.pdf_file}' to '{output_file}'")
        return 0
    except Exception as e:
        print(f"Error during conversion: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == "__main__":
    exit(main())
