#!/usr/bin/env python3
"""
Example script to convert the dock.pdf file to HTML
"""

from pdf_to_html_converter import PDFToHTMLConverter
import os

def main():
    # Check if dock.pdf exists
    pdf_file = "dock.pdf"
    if not os.path.exists(pdf_file):
        print(f"Error: {pdf_file} not found in current directory!")
        return
    
    print(f"Converting {pdf_file} to HTML...")
    
    # Create converter instance
    converter = PDFToHTMLConverter(pdf_file, "terms/html")
    
    try:
        # Convert the PDF
        output_file = converter.convert()
        print(f"\n✅ Conversion successful!")
        print(f"📄 Input: {pdf_file}")
        print(f"🌐 Output: {output_file}")
        print(f"\nYou can now open the HTML file in your browser to view the converted document.")
        
    except Exception as e:
        print(f"❌ Error during conversion: {e}")
        print("Make sure you have installed the required dependencies:")
        print("pip install -r requirements.txt")

if __name__ == "__main__":
    main()
