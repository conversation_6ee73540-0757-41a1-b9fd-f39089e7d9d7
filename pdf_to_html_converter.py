#!/usr/bin/env python3
"""
PDF to HTML Converter
Converts PDF files to HTML while preserving exact formatting, layout, and styling.
"""

import pdfplumber
import argparse
import os
from pathlib import Path
import base64
from typing import Dict, List, Tuple, Optional
import json


class PDFToHTMLConverter:
    def __init__(self, pdf_path: str, output_dir: str = "terms/html"):
        self.pdf_path = pdf_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)
        
        # CSS styles for maintaining exact formatting
        self.base_css = """
        <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .page {
            background-color: white;
            margin: 20px auto;
            padding: 40px;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
        }
        .text-element {
            position: absolute;
            white-space: pre-wrap;
            word-wrap: break-word;
        }
        .line-element {
            position: absolute;
            background-color: black;
        }
        .rect-element {
            position: absolute;
            border: 1px solid black;
        }
        .image-element {
            position: absolute;
        }
        </style>
        """

    def extract_text_with_positions(self, page) -> List[Dict]:
        """Extract text with exact positioning information"""
        text_elements = []
        
        # Get all characters with their positions
        chars = page.chars
        
        # Group characters into words and lines
        current_word = ""
        current_x = None
        current_y = None
        current_font = None
        current_size = None
        
        for char in chars:
            x = char.get('x0', 0)
            y = char.get('top', 0)
            font = char.get('fontname', 'Arial')
            size = char.get('size', 12)
            text = char.get('text', '')
            
            # Check if this character continues the current word
            if (current_x is not None and 
                abs(x - current_x) < size * 0.3 and  # Close horizontally
                abs(y - current_y) < size * 0.2 and  # Close vertically
                font == current_font and 
                size == current_size and
                text != ' '):
                
                current_word += text
                current_x = char.get('x1', x)
            else:
                # Save the previous word if it exists
                if current_word.strip():
                    text_elements.append({
                        'text': current_word,
                        'x': current_x - len(current_word) * size * 0.6,
                        'y': current_y,
                        'font': current_font,
                        'size': current_size
                    })
                
                # Start new word
                if text.strip():
                    current_word = text
                    current_x = char.get('x1', x)
                    current_y = y
                    current_font = font
                    current_size = size
                else:
                    current_word = ""
                    current_x = None
        
        # Don't forget the last word
        if current_word.strip():
            text_elements.append({
                'text': current_word,
                'x': current_x - len(current_word) * current_size * 0.6,
                'y': current_y,
                'font': current_font,
                'size': current_size
            })
        
        return text_elements

    def extract_lines_and_shapes(self, page) -> List[Dict]:
        """Extract lines and rectangular shapes"""
        elements = []
        
        # Extract lines
        lines = page.lines
        for line in lines:
            elements.append({
                'type': 'line',
                'x0': line.get('x0', 0),
                'y0': line.get('top', 0),
                'x1': line.get('x1', 0),
                'y1': line.get('bottom', 0),
                'width': line.get('width', 1)
            })
        
        # Extract rectangles
        rects = page.rects
        for rect in rects:
            elements.append({
                'type': 'rect',
                'x0': rect.get('x0', 0),
                'y0': rect.get('top', 0),
                'x1': rect.get('x1', 0),
                'y1': rect.get('bottom', 0),
                'width': rect.get('linewidth', 1)
            })
        
        return elements

    def extract_images(self, page) -> List[Dict]:
        """Extract images from the page"""
        images = []
        
        # Get images from the page
        page_images = page.images
        for img in page_images:
            try:
                # Extract image data
                image_obj = page.within_bbox(img['bbox']).to_image()
                
                # Convert to base64
                import io
                img_buffer = io.BytesIO()
                image_obj.save(img_buffer, format='PNG')
                img_data = base64.b64encode(img_buffer.getvalue()).decode()
                
                images.append({
                    'x': img['x0'],
                    'y': img['top'],
                    'width': img['width'],
                    'height': img['height'],
                    'data': f"data:image/png;base64,{img_data}"
                })
            except Exception as e:
                print(f"Warning: Could not extract image: {e}")
        
        return images

    def convert_page_to_html(self, page, page_num: int) -> str:
        """Convert a single page to HTML"""
        page_width = page.width
        page_height = page.height
        
        # Extract all elements
        text_elements = self.extract_text_with_positions(page)
        shapes = self.extract_lines_and_shapes(page)
        images = self.extract_images(page)
        
        html_content = f"""
        <div class="page" style="width: {page_width}px; height: {page_height}px;">
        """
        
        # Add text elements
        for text_elem in text_elements:
            x = text_elem['x']
            y = page_height - text_elem['y']  # Flip Y coordinate
            font_size = text_elem['size']
            font_family = text_elem['font']
            text = text_elem['text'].replace('<', '&lt;').replace('>', '&gt;')
            
            html_content += f"""
            <div class="text-element" style="
                left: {x}px; 
                top: {y}px; 
                font-size: {font_size}px; 
                font-family: {font_family};
            ">{text}</div>
            """
        
        # Add lines and shapes
        for shape in shapes:
            if shape['type'] == 'line':
                x0, y0 = shape['x0'], page_height - shape['y0']
                x1, y1 = shape['x1'], page_height - shape['y1']
                width = shape['width']
                
                # Calculate line properties
                length = ((x1 - x0) ** 2 + (y1 - y0) ** 2) ** 0.5
                angle = 0
                if length > 0:
                    import math
                    angle = math.atan2(y1 - y0, x1 - x0) * 180 / math.pi
                
                html_content += f"""
                <div class="line-element" style="
                    left: {x0}px; 
                    top: {y0}px; 
                    width: {length}px; 
                    height: {width}px;
                    transform: rotate({angle}deg);
                    transform-origin: 0 0;
                "></div>
                """
            
            elif shape['type'] == 'rect':
                x0, y0 = shape['x0'], page_height - shape['y1']
                width = shape['x1'] - shape['x0']
                height = shape['y1'] - shape['y0']
                border_width = shape['width']
                
                html_content += f"""
                <div class="rect-element" style="
                    left: {x0}px; 
                    top: {y0}px; 
                    width: {width}px; 
                    height: {height}px;
                    border-width: {border_width}px;
                "></div>
                """
        
        # Add images
        for img in images:
            x = img['x']
            y = page_height - img['y'] - img['height']
            
            html_content += f"""
            <img class="image-element" src="{img['data']}" style="
                left: {x}px; 
                top: {y}px; 
                width: {img['width']}px; 
                height: {img['height']}px;
            ">
            """
        
        html_content += "</div>"
        return html_content

    def convert(self) -> str:
        """Convert the entire PDF to HTML"""
        output_file = self.output_dir / f"{Path(self.pdf_path).stem}.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{Path(self.pdf_path).stem}</title>
            {self.base_css}
        </head>
        <body>
        """
        
        with pdfplumber.open(self.pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}/{len(pdf.pages)}")
                page_html = self.convert_page_to_html(page, page_num)
                html_content += page_html
        
        html_content += """
        </body>
        </html>
        """
        
        # Save the HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"Conversion completed! HTML saved to: {output_file}")
        return str(output_file)


def main():
    parser = argparse.ArgumentParser(description='Convert PDF to HTML with exact formatting')
    parser.add_argument('pdf_file', help='Path to the PDF file to convert')
    parser.add_argument('-o', '--output', default='terms/html', 
                       help='Output directory (default: terms/html)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.pdf_file):
        print(f"Error: PDF file '{args.pdf_file}' not found!")
        return 1
    
    try:
        converter = PDFToHTMLConverter(args.pdf_file, args.output)
        output_file = converter.convert()
        print(f"Successfully converted '{args.pdf_file}' to '{output_file}'")
        return 0
    except Exception as e:
        print(f"Error during conversion: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
