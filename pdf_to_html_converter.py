#!/usr/bin/env python3
"""
PDF to HTML Converter - Enhanced Version
Converts PDF files to HTML while preserving exact formatting, layout, and styling.
Uses improved text extraction and positioning algorithms.
"""

import pdfplumber
import argparse
import os
from pathlib import Path
import base64
from typing import Dict, List, Tuple, Optional
import json
import re


class PDFToHTMLConverter:
    def __init__(self, pdf_path: str, output_dir: str = "terms/html"):
        self.pdf_path = pdf_path
        self.output_dir = Path(output_dir)
        self.output_dir.mkdir(parents=True, exist_ok=True)

        # CSS styles for maintaining exact formatting
        self.base_css = """
        <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .page {
            background-color: white;
            margin: 20px auto;
            padding: 0;
            box-shadow: 0 0 10px rgba(0,0,0,0.1);
            position: relative;
            overflow: hidden;
            page-break-after: always;
        }
        .text-element {
            position: absolute;
            white-space: nowrap;
            font-family: inherit;
            line-height: 1;
        }
        .text-block {
            position: absolute;
            white-space: pre-wrap;
            word-wrap: break-word;
            font-family: inherit;
        }
        .line-element {
            position: absolute;
            background-color: black;
        }
        .rect-element {
            position: absolute;
            border: 1px solid black;
            background-color: transparent;
        }
        .image-element {
            position: absolute;
        }
        .highlight {
            position: absolute;
            background-color: yellow;
            opacity: 0.3;
        }
        @media print {
            .page {
                margin: 0;
                box-shadow: none;
                page-break-after: always;
            }
        }
        </style>
        """

    def extract_text_with_positions(self, page) -> List[Dict]:
        """Extract text with exact positioning information using improved algorithm"""
        text_elements = []

        # Get all characters with their positions
        chars = page.chars
        if not chars:
            return text_elements

        # Sort characters by position (top to bottom, left to right)
        chars = sorted(chars, key=lambda c: (round(c.get('top', 0), 1), c.get('x0', 0)))

        # Group characters into text blocks
        current_line = []
        current_y = None
        tolerance_y = 2  # Vertical tolerance for same line

        for char in chars:
            x0 = char.get('x0', 0)
            y = char.get('top', 0)
            font = char.get('fontname', 'Arial')
            size = char.get('size', 12)
            text = char.get('text', '')

            # Skip empty or whitespace-only characters in some cases
            if not text or text.isspace():
                if text == ' ' and current_line:
                    current_line.append(char)
                continue

            # Check if this character is on the same line
            if current_y is None or abs(y - current_y) <= tolerance_y:
                current_line.append(char)
                current_y = y
            else:
                # Process the current line
                if current_line:
                    line_text = self._process_character_line(current_line)
                    if line_text:
                        text_elements.extend(line_text)

                # Start new line
                current_line = [char]
                current_y = y

        # Process the last line
        if current_line:
            line_text = self._process_character_line(current_line)
            if line_text:
                text_elements.extend(line_text)

        return text_elements

    def _process_character_line(self, chars: List[Dict]) -> List[Dict]:
        """Process a line of characters into text elements"""
        if not chars:
            return []

        text_elements = []
        current_word = ""
        word_chars = []

        for i, char in enumerate(chars):
            text = char.get('text', '')

            if text.isspace():
                # End current word
                if current_word and word_chars:
                    element = self._create_text_element(word_chars, current_word)
                    if element:
                        text_elements.append(element)

                current_word = ""
                word_chars = []
            else:
                current_word += text
                word_chars.append(char)

        # Don't forget the last word
        if current_word and word_chars:
            element = self._create_text_element(word_chars, current_word)
            if element:
                text_elements.append(element)

        return text_elements

    def _create_text_element(self, chars: List[Dict], text: str) -> Optional[Dict]:
        """Create a text element from a group of characters"""
        if not chars or not text.strip():
            return None

        first_char = chars[0]
        last_char = chars[-1]

        # Calculate average font size and get font name
        avg_size = sum(c.get('size', 12) for c in chars) / len(chars)
        font_name = first_char.get('fontname', 'Arial')

        # Position based on first character
        x = first_char.get('x0', 0)
        y = first_char.get('top', 0)

        # Calculate width based on character positions
        width = last_char.get('x1', x) - x

        return {
            'text': text,
            'x': x,
            'y': y,
            'font': font_name,
            'size': avg_size,
            'width': width
        }

    def extract_lines_and_shapes(self, page) -> List[Dict]:
        """Extract lines and rectangular shapes"""
        elements = []
        
        # Extract lines
        lines = page.lines
        for line in lines:
            elements.append({
                'type': 'line',
                'x0': line.get('x0', 0),
                'y0': line.get('top', 0),
                'x1': line.get('x1', 0),
                'y1': line.get('bottom', 0),
                'width': line.get('width', 1)
            })
        
        # Extract rectangles
        rects = page.rects
        for rect in rects:
            elements.append({
                'type': 'rect',
                'x0': rect.get('x0', 0),
                'y0': rect.get('top', 0),
                'x1': rect.get('x1', 0),
                'y1': rect.get('bottom', 0),
                'width': rect.get('linewidth', 1)
            })
        
        return elements

    def extract_images(self, page) -> List[Dict]:
        """Extract images from the page"""
        images = []
        
        # Get images from the page
        page_images = page.images
        for img in page_images:
            try:
                # Extract image data
                image_obj = page.within_bbox(img['bbox']).to_image()
                
                # Convert to base64
                import io
                img_buffer = io.BytesIO()
                image_obj.save(img_buffer, format='PNG')
                img_data = base64.b64encode(img_buffer.getvalue()).decode()
                
                images.append({
                    'x': img['x0'],
                    'y': img['top'],
                    'width': img['width'],
                    'height': img['height'],
                    'data': f"data:image/png;base64,{img_data}"
                })
            except Exception as e:
                print(f"Warning: Could not extract image: {e}")
        
        return images

    def convert_page_to_html(self, page, page_num: int) -> str:
        """Convert a single page to HTML with improved positioning"""
        page_width = page.width
        page_height = page.height

        # Extract all elements
        text_elements = self.extract_text_with_positions(page)
        shapes = self.extract_lines_and_shapes(page)
        images = self.extract_images(page)

        html_content = f"""
        <div class="page" style="width: {page_width}px; height: {page_height}px;">
        """

        # Add text elements with better positioning
        for text_elem in text_elements:
            x = text_elem['x']
            y = text_elem['y']  # Keep original Y coordinate (PDF coordinate system)
            font_size = text_elem['size']
            font_family = self._normalize_font_name(text_elem['font'])
            text = self._escape_html(text_elem['text'])

            # Adjust font weight and style based on font name
            font_weight = 'bold' if 'bold' in font_family.lower() else 'normal'
            font_style = 'italic' if 'italic' in font_family.lower() else 'normal'

            html_content += f"""
            <div class="text-element" style="
                left: {x:.1f}px;
                top: {y:.1f}px;
                font-size: {font_size:.1f}px;
                font-family: {font_family};
                font-weight: {font_weight};
                font-style: {font_style};
            ">{text}</div>
            """

        # Add lines and shapes with better precision
        for shape in shapes:
            if shape['type'] == 'line':
                x0, y0 = shape['x0'], shape['y0']
                x1, y1 = shape['x1'], shape['y1']
                line_width = max(shape['width'], 0.5)

                # Calculate line properties
                length = ((x1 - x0) ** 2 + (y1 - y0) ** 2) ** 0.5
                if length > 0:
                    import math
                    angle = math.atan2(y1 - y0, x1 - x0) * 180 / math.pi

                    html_content += f"""
                    <div class="line-element" style="
                        left: {x0:.1f}px;
                        top: {y0:.1f}px;
                        width: {length:.1f}px;
                        height: {line_width:.1f}px;
                        transform: rotate({angle:.1f}deg);
                        transform-origin: 0 0;
                    "></div>
                    """

            elif shape['type'] == 'rect':
                x0, y0 = shape['x0'], shape['y0']
                width = shape['x1'] - shape['x0']
                height = shape['y1'] - shape['y0']
                border_width = max(shape['width'], 0.5)

                html_content += f"""
                <div class="rect-element" style="
                    left: {x0:.1f}px;
                    top: {y0:.1f}px;
                    width: {width:.1f}px;
                    height: {height:.1f}px;
                    border-width: {border_width:.1f}px;
                "></div>
                """

        # Add images with precise positioning
        for img in images:
            x = img['x']
            y = img['y']

            html_content += f"""
            <img class="image-element" src="{img['data']}" style="
                left: {x:.1f}px;
                top: {y:.1f}px;
                width: {img['width']:.1f}px;
                height: {img['height']:.1f}px;
            ">
            """

        html_content += "</div>"
        return html_content

    def _normalize_font_name(self, font_name: str) -> str:
        """Normalize font names to web-safe fonts"""
        font_name = font_name.lower()

        if 'times' in font_name:
            return 'Times, "Times New Roman", serif'
        elif 'helvetica' in font_name or 'arial' in font_name:
            return 'Arial, Helvetica, sans-serif'
        elif 'courier' in font_name:
            return 'Courier, "Courier New", monospace'
        else:
            return 'Arial, Helvetica, sans-serif'

    def _escape_html(self, text: str) -> str:
        """Escape HTML special characters"""
        return (text.replace('&', '&amp;')
                   .replace('<', '&lt;')
                   .replace('>', '&gt;')
                   .replace('"', '&quot;')
                   .replace("'", '&#x27;'))

    def convert(self) -> str:
        """Convert the entire PDF to HTML"""
        output_file = self.output_dir / f"{Path(self.pdf_path).stem}.html"
        
        html_content = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>{Path(self.pdf_path).stem}</title>
            {self.base_css}
        </head>
        <body>
        """
        
        with pdfplumber.open(self.pdf_path) as pdf:
            for page_num, page in enumerate(pdf.pages):
                print(f"Processing page {page_num + 1}/{len(pdf.pages)}")
                page_html = self.convert_page_to_html(page, page_num)
                html_content += page_html
        
        html_content += """
        </body>
        </html>
        """
        
        # Save the HTML file
        with open(output_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"Conversion completed! HTML saved to: {output_file}")
        return str(output_file)


def main():
    parser = argparse.ArgumentParser(description='Convert PDF to HTML with exact formatting')
    parser.add_argument('pdf_file', help='Path to the PDF file to convert')
    parser.add_argument('-o', '--output', default='terms/html', 
                       help='Output directory (default: terms/html)')
    
    args = parser.parse_args()
    
    if not os.path.exists(args.pdf_file):
        print(f"Error: PDF file '{args.pdf_file}' not found!")
        return 1
    
    try:
        converter = PDFToHTMLConverter(args.pdf_file, args.output)
        output_file = converter.convert()
        print(f"Successfully converted '{args.pdf_file}' to '{output_file}'")
        return 0
    except Exception as e:
        print(f"Error during conversion: {e}")
        return 1


if __name__ == "__main__":
    exit(main())
