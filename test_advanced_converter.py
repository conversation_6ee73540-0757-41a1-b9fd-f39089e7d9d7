#!/usr/bin/env python3
"""
Test script for the advanced PDF to HTML converter
"""

from pdf_to_html_advanced import AdvancedPDFToHTMLConverter
import os

def main():
    # Check if dock.pdf exists
    pdf_file = "dock.pdf"
    if not os.path.exists(pdf_file):
        print(f"Error: {pdf_file} not found in current directory!")
        return
    
    print(f"🚀 Converting {pdf_file} with ADVANCED algorithm...")
    print("This version uses improved text extraction and layout preservation.")
    
    # Create advanced converter instance
    converter = AdvancedPDFToHTMLConverter(pdf_file, "terms/html")
    
    try:
        # Convert the PDF
        output_file = converter.convert()
        print(f"\n✅ Advanced conversion successful!")
        print(f"📄 Input: {pdf_file}")
        print(f"🌐 Output: {output_file}")
        print(f"\n🎯 Key improvements in this version:")
        print(f"   • Better text block extraction")
        print(f"   • Improved word spacing")
        print(f"   • Enhanced font handling")
        print(f"   • Layered rendering (shapes + text)")
        print(f"   • More precise positioning")
        print(f"\nYou can now open the HTML file in your browser to view the improved conversion.")
        
    except Exception as e:
        print(f"❌ Error during advanced conversion: {e}")
        print("Make sure you have installed the required dependencies:")
        print("pip install -r requirements.txt")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
